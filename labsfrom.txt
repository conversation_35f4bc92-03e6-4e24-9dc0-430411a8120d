<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LabsForm Pro - Formatação de Exames Médicos</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/jspdf@2.5.1/dist/jspdf.umd.min.js"></script>
    <style>
        .medical-card {
            transition: all 0.3s ease;
        }
        .medical-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .dropzone {
            border: 2px dashed #cbd5e0;
            transition: all 0.3s ease;
        }
        .dropzone.dragover {
            border-color: #4299e1;
            background-color: #ebf8ff;
        }
        .result-textarea {
            font-family: 'Courier New', monospace;
            line-height: 1.6;
        }
        .alert-box {
            background: linear-gradient(135deg, #fef3c7 0%, #fcd34d 100%);
        }
        .processing-spinner {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .pulse-success {
            animation: pulseSuccess 0.6s ease-in-out;
        }
        @keyframes pulseSuccess {
            0% { background-color: #10b981; }
            50% { background-color: #34d399; }
            100% { background-color: #10b981; }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-gradient-to-r from-slate-600 to-slate-700 text-white shadow-lg">
        <div class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-microscope text-2xl"></i>
                    <h1 class="text-xl font-medium">
                        Fluxograma <span class="text-sm opacity-80">(Pro)</span>
                    </h1>
                </div>
                <div class="text-center">
                    <h2 class="text-2xl font-light">LabsForm Pro</h2>
                    <p class="text-sm opacity-90">Formatação Automática de Exames</p>
                </div>
                <div class="flex items-center space-x-2 text-sm">
                    <i class="fas fa-shield-alt"></i>
                    <span>Seguro & Privado</span>
                </div>
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8 max-w-6xl">
        <!-- Título da Seção -->
        <div class="text-center mb-8">
            <h3 class="text-3xl font-bold text-gray-800 mb-2">Formatar Exames</h3>
            <p class="text-gray-600">Cole texto ou faça upload de PDFs para formatação automática</p>
        </div>

        <!-- Métodos de Input -->
        <div class="grid md:grid-cols-2 gap-8 mb-8">
            <!-- Copy-Paste Method -->
            <div class="medical-card bg-white rounded-lg shadow-lg p-6">
                <div class="mb-4">
                    <h4 class="text-xl font-semibold text-gray-800 mb-2 flex items-center">
                        <i class="fas fa-clipboard text-blue-600 mr-2"></i>
                        Método Copy-Paste
                    </h4>
                    <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-4">
                        <p class="text-sm text-blue-800">
                            <strong>Instruções:</strong> Na intranet do laboratório, pressione 
                            <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Ctrl+A</kbd> e 
                            <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Ctrl+C</kbd>, 
                            depois <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Ctrl+V</kbd> no campo abaixo:
                        </p>
                    </div>
                </div>
                <textarea 
                    id="textInput" 
                    class="w-full h-40 p-4 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none transition-colors"
                    placeholder="Cole aqui o conteúdo dos exames laboratoriais...&#10;&#10;Exemplo:&#10;HEMOGRAMA COMPLETO&#10;Hemoglobina: 14.2 g/dL&#10;Hematócrito: 42%&#10;Leucócitos: 7.500/mm³"
                ></textarea>
                <button 
                    id="processTextBtn" 
                    class="mt-4 w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors flex items-center justify-center"
                >
                    <i class="fas fa-cogs mr-2"></i>
                    Processar Texto
                </button>
            </div>

            <!-- File Upload Method -->
            <div class="medical-card bg-white rounded-lg shadow-lg p-6">
                <div class="mb-4">
                    <h4 class="text-xl font-semibold text-gray-800 mb-2 flex items-center">
                        <i class="fas fa-file-upload text-green-600 mr-2"></i>
                        Upload de Arquivos
                    </h4>
                    <p class="text-sm text-gray-600 mb-4">
                        Suporte para PDFs e imagens (PNG, JPG) - máximo 10MB por arquivo
                    </p>
                </div>
                <div 
                    id="dropzone" 
                    class="dropzone border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-blue-400 transition-colors"
                >
                    <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-600 mb-2">Arraste arquivos aqui ou clique para selecionar</p>
                    <p class="text-sm text-gray-500">PDF, PNG, JPG (máx. 10MB cada)</p>
                    <input type="file" id="fileInput" multiple accept=".pdf,.png,.jpg,.jpeg" class="hidden">
                </div>
                <div id="fileList" class="mt-4 space-y-2"></div>
                <button 
                    id="processFilesBtn" 
                    class="mt-4 w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors flex items-center justify-center"
                    disabled
                >
                    <i class="fas fa-file-medical mr-2"></i>
                    Processar Arquivos
                </button>
            </div>
        </div>

        <!-- Processing Indicator -->
        <div id="processingIndicator" class="hidden bg-white rounded-lg shadow-lg p-6 mb-8 text-center">
            <div class="flex items-center justify-center mb-4">
                <i class="fas fa-spinner processing-spinner text-3xl text-blue-600 mr-3"></i>
                <span class="text-lg font-semibold text-gray-800">Processando exames...</span>
            </div>
            <div class="bg-gray-200 rounded-full h-2">
                <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
            </div>
            <p class="text-sm text-gray-600 mt-2">Analisando e formatando resultados...</p>
        </div>

        <!-- Results Section -->
        <div id="resultsSection" class="hidden fade-in">
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-xl font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-clipboard-check text-green-600 mr-2"></i>
                        Resultado Formatado
                    </h4>
                    <div class="flex space-x-2">
                        <button 
                            id="copyBtn" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center"
                        >
                            <i class="fas fa-copy mr-2"></i>
                            Copiar
                        </button>
                        <button 
                            id="exportPdfBtn" 
                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center"
                        >
                            <i class="fas fa-file-pdf mr-2"></i>
                            Exportar PDF
                        </button>
                        <button 
                            id="clearBtn" 
                            class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center"
                        >
                            <i class="fas fa-trash mr-2"></i>
                            Limpar
                        </button>
                    </div>
                </div>
                <textarea 
                    id="resultOutput" 
                    class="result-textarea w-full h-64 p-4 bg-gray-50 border-2 border-gray-300 rounded-lg focus:border-green-500 focus:outline-none transition-colors text-sm"
                    readonly
                    placeholder="O resultado formatado aparecerá aqui..."
                ></textarea>
            </div>

            <!-- Valores Alterados -->
            <div id="alertsSection" class="hidden bg-white rounded-lg shadow-lg p-6 mb-8">
                <h4 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-exclamation-triangle text-yellow-600 mr-2"></i>
                    Valores Fora da Referência
                </h4>
                <div id="alertsList" class="space-y-2"></div>
            </div>
        </div>

        <!-- Instruções e Alertas -->
        <div class="alert-box rounded-lg shadow-lg p-6 border border-yellow-300">
            <div class="flex items-start">
                <i class="fas fa-lightbulb text-3xl text-yellow-700 mr-4 mt-1"></i>
                <div>
                    <h4 class="text-xl font-bold text-yellow-800 mb-4">ATENÇÃO!</h4>
                    <ul class="text-yellow-800 space-y-2 text-sm leading-relaxed">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-yellow-600 mr-2 mt-1 text-xs"></i>
                            Sempre revise se todos os exames e resultados estão incluídos no resultado formatado
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-yellow-600 mr-2 mt-1 text-xs"></i>
                            Sempre confira se todos os valores estão corretos e foram interpretados adequadamente
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-user-md text-yellow-600 mr-2 mt-1 text-xs"></i>
                            Lembre-se: uma evolução adequada é sua responsabilidade profissional - revise sempre!
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-envelope text-yellow-600 mr-2 mt-1 text-xs"></i>
                            Envie sugestões e erros encontrados para: 
                            <a href="mailto:<EMAIL>" class="underline hover:text-yellow-900"><EMAIL></a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Histórico de Exames -->
        <div id="historySection" class="mt-8 bg-white rounded-lg shadow-lg p-6">
            <div class="flex justify-between items-center mb-4">
                <h4 class="text-xl font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-history text-purple-600 mr-2"></i>
                    Histórico de Processamentos
                </h4>
                <button 
                    id="clearHistoryBtn" 
                    class="text-red-600 hover:text-red-700 text-sm flex items-center"
                >
                    <i class="fas fa-trash-alt mr-1"></i>
                    Limpar Histórico
                </button>
            </div>
            <div id="historyList" class="space-y-3">
                <p class="text-gray-500 text-center py-8">Nenhum exame processado ainda.</p>
            </div>
        </div>
    </main>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <script>
        class LabsFormPro {
            constructor() {
                this.history = JSON.parse(localStorage.getItem('labsFormHistory') || '[]');
                this.referenceValues = {
                    hemoglobina: { min: 12.0, max: 15.5, unit: 'g/dL' },
                    hematocrito: { min: 36, max: 46, unit: '%' },
                    leucocitos: { min: 4000, max: 11000, unit: '/mm³' },
                    plaquetas: { min: 150000, max: 450000, unit: '/mm³' },
                    glicose: { min: 70, max: 99, unit: 'mg/dL' },
                    creatinina: { min: 0.6, max: 1.3, unit: 'mg/dL' },
                    ureia: { min: 15, max: 45, unit: 'mg/dL' },
                    colesterol: { min: 0, max: 200, unit: 'mg/dL' }
                };
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.loadHistory();
            }

            setupEventListeners() {
                // Text processing
                document.getElementById('processTextBtn').addEventListener('click', () => this.processText());
                document.getElementById('textInput').addEventListener('input', (e) => {
                    const hasText = e.target.value.trim().length > 0;
                    document.getElementById('processTextBtn').disabled = !hasText;
                });

                // File upload
                const dropzone = document.getElementById('dropzone');
                const fileInput = document.getElementById('fileInput');
                
                dropzone.addEventListener('click', () => fileInput.click());
                dropzone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    dropzone.classList.add('dragover');
                });
                dropzone.addEventListener('dragleave', () => {
                    dropzone.classList.remove('dragover');
                });
                dropzone.addEventListener('drop', (e) => {
                    e.preventDefault();
                    dropzone.classList.remove('dragover');
                    this.handleFiles(e.dataTransfer.files);
                });
                
                fileInput.addEventListener('change', (e) => this.handleFiles(e.target.files));
                document.getElementById('processFilesBtn').addEventListener('click', () => this.processFiles());

                // Result actions
                document.getElementById('copyBtn').addEventListener('click', () => this.copyResult());
                document.getElementById('exportPdfBtn').addEventListener('click', () => this.exportToPDF());
                document.getElementById('clearBtn').addEventListener('click', () => this.clearResults());
                document.getElementById('clearHistoryBtn').addEventListener('click', () => this.clearHistory());
            }

            handleFiles(files) {
                const fileList = document.getElementById('fileList');
                const processBtn = document.getElementById('processFilesBtn');
                
                fileList.innerHTML = '';
                
                Array.from(files).forEach((file, index) => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'flex items-center justify-between bg-gray-50 p-3 rounded-lg';
                    fileItem.innerHTML = `
                        <div class="flex items-center">
                            <i class="fas fa-file-${file.type.includes('pdf') ? 'pdf' : 'image'} text-red-600 mr-2"></i>
                            <span class="text-sm font-medium">${file.name}</span>
                            <span class="text-xs text-gray-500 ml-2">(${(file.size / 1024 / 1024).toFixed(1)} MB)</span>
                        </div>
                        <button class="text-red-600 hover:text-red-700" onclick="this.parentElement.remove(); this.updateProcessBtn()">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                    fileList.appendChild(fileItem);
                });

                processBtn.disabled = files.length === 0;
            }

            async processText() {
                const text = document.getElementById('textInput').value.trim();
                if (!text) return;

                this.showProcessing();
                
                // Simulate processing delay
                await this.delay(1500);
                
                const result = this.parseLabResults(text);
                this.displayResults(result);
                this.saveToHistory(result, 'texto');
            }

            async processFiles() {
                const fileInput = document.getElementById('fileInput');
                if (!fileInput.files.length) return;

                this.showProcessing();
                
                // Simulate file processing
                await this.delay(2500);
                
                // For demo purposes, we'll use sample text to simulate OCR results
                const sampleText = `
                    HEMOGRAMA COMPLETO
                    Hemoglobina: 13.8 g/dL
                    Hematócrito: 41%
                    Eritrócitos: 4.5 milhões/mm³
                    Leucócitos: 6.800/mm³
                    Plaquetas: 280.000/mm³
                    
                    BIOQUÍMICA SÉRICA
                    Glicose: 92 mg/dL
                    Creatinina: 0.9 mg/dL
                    Uréia: 28 mg/dL
                    Colesterol Total: 185 mg/dL
                `;
                
                const result = this.parseLabResults(sampleText);
                this.displayResults(result);
                this.saveToHistory(result, 'arquivo');
            }

            parseLabResults(text) {
                const patterns = {
                    hemograma: {
                        hemoglobina: /(?:hemoglobina|hb)[:\s]*([0-9,.\s]+)(?:\s*g\/dl)/gi,
                        hematocrito: /(?:hematócrito|ht)[:\s]*([0-9,.\s]+)%/gi,
                        eritrocitos: /(?:eritrócitos|hemácias)[:\s]*([0-9,.\s]+)(?:\s*milhões\/mm³)/gi,
                        leucocitos: /(?:leucócitos)[:\s]*([0-9,.]+)(?:\s*\/mm³)/gi,
                        plaquetas: /(?:plaquetas)[:\s]*([0-9,.]+)(?:\s*\/mm³)/gi
                    },
                    bioquimica: {
                        glicose: /(?:glicose)[:\s]*([0-9,.\s]+)(?:\s*mg\/dl)/gi,
                        creatinina: /(?:creatinina)[:\s]*([0-9,.\s]+)(?:\s*mg\/dl)/gi,
                        ureia: /(?:uréia|ureia)[:\s]*([0-9,.\s]+)(?:\s*mg\/dl)/gi,
                        colesterol: /(?:colesterol total)[:\s]*([0-9,.\s]+)(?:\s*mg\/dl)/gi
                    }
                };

                const results = {
                    hemograma: {},
                    bioquimica: {},
                    formatted: '',
                    alerts: [],
                    rawText: text
                };

                // Parse each category
                Object.keys(patterns).forEach(category => {
                    Object.keys(patterns[category]).forEach(test => {
                        const matches = text.match(patterns[category][test]);
                        if (matches && matches.length > 0) {
                            const value = this.extractValue(matches[0]);
                            results[category][test] = value;
                            this.checkReferenceValue(test, value, results.alerts);
                        }
                    });
                });

                results.formatted = this.formatResults(results);
                return results;
            }

            extractValue(match) {
                return match.split(/[:\s]+/).pop().trim().replace(',', '.');
            }

            checkReferenceValue(test, value, alerts) {
                if (this.referenceValues[test]) {
                    const ref = this.referenceValues[test];
                    const numValue = parseFloat(value);
                    
                    if (!isNaN(numValue)) {
                        if (numValue < ref.min) {
                            alerts.push({
                                test: test,
                                value: value,
                                status: 'baixo',
                                reference: `${ref.min}-${ref.max}`,
                                unit: ref.unit
                            });
                        } else if (numValue > ref.max) {
                            alerts.push({
                                test: test,
                                value: value,
                                status: 'alto',
                                reference: `${ref.min}-${ref.max}`,
                                unit: ref.unit
                            });
                        }
                    }
                }
            }

            formatResults(results) {
                let formatted = [];

                // Hemograma
                if (Object.keys(results.hemograma).length > 0) {
                    const hemo = results.hemograma;
                    let line = 'HEMOGRAMA: ';
                    const parts = [];
                    
                    if (hemo.hemoglobina) parts.push(`Hb ${hemo.hemoglobina}g/dL`);
                    if (hemo.hematocrito) parts.push(`Ht ${hemo.hematocrito}%`);
                    if (hemo.eritrocitos) parts.push(`Hem ${hemo.eritrocitos}`);
                    if (hemo.leucocitos) parts.push(`Leuc ${hemo.leucocitos}`);
                    if (hemo.plaquetas) parts.push(`Plaq ${hemo.plaquetas}`);
                    
                    line += parts.join(' / ');
                    formatted.push(line);
                }

                // Bioquímica
                if (Object.keys(results.bioquimica).length > 0) {
                    const bio = results.bioquimica;
                    let line = 'BIOQUÍMICA: ';
                    const parts = [];
                    
                    if (bio.glicose) parts.push(`Gli ${bio.glicose}mg/dL`);
                    if (bio.creatinina) parts.push(`Cr ${bio.creatinina}mg/dL`);
                    if (bio.ureia) parts.push(`Ureia ${bio.ureia}mg/dL`);
                    if (bio.colesterol) parts.push(`CT ${bio.colesterol}mg/dL`);
                    
                    line += parts.join(' / ');
                    formatted.push(line);
                }

                return formatted.join('\n');
            }

            displayResults(results) {
                document.getElementById('processingIndicator').classList.add('hidden');
                document.getElementById('resultsSection').classList.remove('hidden');
                document.getElementById('resultOutput').value = results.formatted;

                // Display alerts
                if (results.alerts.length > 0) {
                    const alertsSection = document.getElementById('alertsSection');
                    const alertsList = document.getElementById('alertsList');
                    
                    alertsList.innerHTML = '';
                    results.alerts.forEach(alert => {
                        const alertDiv = document.createElement('div');
                        alertDiv.className = `p-3 rounded-lg border-l-4 ${
                            alert.status === 'alto' ? 'bg-red-50 border-red-500' : 'bg-blue-50 border-blue-500'
                        }`;
                        alertDiv.innerHTML = `
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-triangle ${
                                    alert.status === 'alto' ? 'text-red-600' : 'text-blue-600'
                                } mr-2"></i>
                                <span class="font-semibold">${alert.test.toUpperCase()}:</span>
                                <span class="ml-2">${alert.value} ${alert.unit} - ${alert.status.toUpperCase()}</span>
                                <span class="ml-2 text-gray-600">(ref: ${alert.reference} ${alert.unit})</span>
                            </div>
                        `;
                        alertsList.appendChild(alertDiv);
                    });
                    
                    alertsSection.classList.remove('hidden');
                } else {
                    document.getElementById('alertsSection').classList.add('hidden');
                }
            }

            async showProcessing() {
                document.getElementById('processingIndicator').classList.remove('hidden');
                const progressBar = document.getElementById('progressBar');
                
                for (let i = 0; i <= 100; i += 10) {
                    progressBar.style.width = i + '%';
                    await this.delay(150);
                }
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }

            async copyResult() {
                const result = document.getElementById('resultOutput').value;
                try {
                    await navigator.clipboard.writeText(result);
                    this.showToast('Resultado copiado para a área de transferência!', 'success');
                    
                    // Visual feedback
                    const btn = document.getElementById('copyBtn');
                    btn.classList.add('pulse-success');
                    setTimeout(() => btn.classList.remove('pulse-success'), 600);
                } catch (err) {
                    this.showToast('Erro ao copiar resultado', 'error');
                }
            }

            exportToPDF() {
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();
                
                // Header
                doc.setFontSize(18);
                doc.text('Resumo de Exames Laboratoriais', 20, 20);
                
                doc.setFontSize(12);
                doc.text(`Processado em: ${new Date().toLocaleString('pt-BR')}`, 20, 35);
                doc.text('Gerado por: LabsForm Pro', 20, 45);
                
                // Content
                const result = document.getElementById('resultOutput').value;
                const lines = doc.splitTextToSize(result, 170);
                doc.setFontSize(10);
                doc.text(lines, 20, 60);
                
                // Footer
                doc.setFontSize(8);
                doc.text('IMPORTANTE: Sempre revise os resultados antes do uso clínico', 20, 280);
                
                doc.save(`exames_${new Date().toISOString().split('T')[0]}.pdf`);
                this.showToast('PDF exportado com sucesso!', 'success');
            }

            clearResults() {
                document.getElementById('resultsSection').classList.add('hidden');
                document.getElementById('textInput').value = '';
                document.getElementById('fileInput').value = '';
                document.getElementById('fileList').innerHTML = '';
                document.getElementById('processTextBtn').disabled = true;
                document.getElementById('processFilesBtn').disabled = true;
            }

            saveToHistory(result, type) {
                const entry = {
                    id: Date.now(),
                    type: type,
                    result: result.formatted,
                    timestamp: new Date().toISOString(),
                    preview: result.formatted.substring(0, 100) + '...',
                    alerts: result.alerts.length
                };
                
                this.history.unshift(entry);
                this.history = this.history.slice(0, 20); // Keep only last 20
                localStorage.setItem('labsFormHistory', JSON.stringify(this.history));
                this.loadHistory();
            }

            loadHistory() {
                const historyList = document.getElementById('historyList');
                
                if (this.history.length === 0) {
                    historyList.innerHTML = '<p class="text-gray-500 text-center py-8">Nenhum exame processado ainda.</p>';
                    return;
                }
                
                historyList.innerHTML = '';
                this.history.forEach(entry => {
                    const historyItem = document.createElement('div');
                    historyItem.className = 'bg-gray-50 p-4 rounded-lg border hover:bg-gray-100 transition-colors cursor-pointer';
                    historyItem.innerHTML = `
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-${entry.type === 'arquivo' ? 'file' : 'clipboard'} text-gray-600 mr-2"></i>
                                    <span class="font-medium text-gray-800">${entry.type === 'arquivo' ? 'Arquivo' : 'Texto'}</span>
                                    ${entry.alerts > 0 ? `<span class="ml-2 bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">${entry.alerts} alerta(s)</span>` : ''}
                                </div>
                                <p class="text-sm text-gray-600 mb-2">${entry.preview}</p>
                                <p class="text-xs text-gray-500">${new Date(entry.timestamp).toLocaleString('pt-BR')}</p>
                            </div>
                            <button class="text-blue-600 hover:text-blue-700 ml-4" onclick="labsForm.loadFromHistory('${entry.id}')">
                                <i class="fas fa-redo"></i>
                            </button>
                        </div>
                    `;
                    historyList.appendChild(historyItem);
                });
            }

            loadFromHistory(id) {
                const entry = this.history.find(item => item.id.toString() === id);
                if (entry) {
                    document.getElementById('resultOutput').value = entry.result;
                    document.getElementById('resultsSection').classList.remove('hidden');
                    this.showToast('Resultado carregado do histórico!', 'info');
                }
            }

            clearHistory() {
                this.history = [];
                localStorage.removeItem('labsFormHistory');
                this.loadHistory();
                this.showToast('Histórico limpo!', 'info');
            }

            showToast(message, type = 'info') {
                const toast = document.createElement('div');
                const colors = {
                    success: 'bg-green-500',
                    error: 'bg-red-500',
                    info: 'bg-blue-500'
                };
                
                toast.className = `${colors[type]} text-white px-6 py-3 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full`;
                toast.innerHTML = `
                    <div class="flex items-center">
                        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'} mr-2"></i>
                        ${message}
                    </div>
                `;
                
                document.getElementById('toastContainer').appendChild(toast);
                
                setTimeout(() => toast.classList.remove('translate-x-full'), 100);
                setTimeout(() => {
                    toast.classList.add('translate-x-full');
                    setTimeout(() => toast.remove(), 300);
                }, 3000);
            }
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize the application when DOM is fully loaded
            window.labsForm = new LabsFormPro();

            // Show welcome message
            labsForm.showToast('Bem-vindo ao LabsForm Pro!', 'info');
        });
    </script>
</body>
</html>


<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LabsForm Pro Ultra - Formatação Inteligente de Exames Médicos</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <style>
        .medical-gradient {
            background: linear-gradient(135deg, #5a6c7d 0%, #4a5a6b 100%);
        }
        .exam-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(90, 108, 125, 0.1);
        }
        .result-text {
            font-family: 'Courier New', monospace;
            line-height: 1.6;
        }
        .alert-box {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        .dropzone {
            border: 2px dashed #d1d5db;
            transition: all 0.3s ease;
        }
        .dropzone.dragover {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        @media print {
            .no-print { display: none !important; }
            body { -webkit-print-color-adjust: exact; }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="medical-gradient text-white shadow-lg">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <h1 class="text-xl font-medium">Fluxograma <span class="text-sm opacity-80">(beta)</span></h1>
                </div>
                <div class="text-center">
                    <h2 class="text-2xl font-light">LabsForm Pro Ultra</h2>
                    <p class="text-xs opacity-80">Formatação Inteligente com IA</p>
                </div>
                <div class="flex items-center space-x-3">
                    <i class="fas fa-microscope text-2xl"></i>
                </div>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-4 py-6 max-w-7xl">
        <!-- Input Section -->
        <div class="exam-card rounded-lg shadow-lg p-6 mb-6">
            <div class="text-center mb-6">
                <h3 class="text-2xl font-semibold text-gray-800 mb-2">Formatar Exames</h3>
                <p class="text-gray-600">Cole os dados dos exames ou faça upload de arquivos PDF/imagem</p>
            </div>

            <div class="grid md:grid-cols-2 gap-6">
                <!-- Copy-Paste Method -->
                <div class="space-y-4">
                    <h4 class="font-semibold text-gray-700 flex items-center">
                        <i class="fas fa-clipboard mr-2"></i>
                        Método Copy-Paste (Original)
                    </h4>
                    <div class="bg-blue-50 border-l-4 border-blue-400 p-4 rounded">
                        <p class="text-sm text-blue-800">
                            Na intranet: <kbd class="bg-gray-100 px-2 py-1 rounded text-xs">Ctrl+A</kbd> e 
                            <kbd class="bg-gray-100 px-2 py-1 rounded text-xs">Ctrl+C</kbd>; 
                            depois <kbd class="bg-gray-100 px-2 py-1 rounded text-xs">Ctrl+V</kbd> no campo abaixo:
                        </p>
                    </div>
                    <textarea 
                        id="examData" 
                        class="w-full h-32 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                        placeholder="Cole aqui o conteúdo dos exames laboratoriais..."
                    ></textarea>
                </div>

                <!-- File Upload Method -->
                <div class="space-y-4">
                    <h4 class="font-semibold text-gray-700 flex items-center">
                        <i class="fas fa-upload mr-2"></i>
                        Upload de Arquivos PDF/Imagem
                    </h4>
                    <div 
                        id="dropzone" 
                        class="dropzone border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:bg-gray-50"
                    >
                        <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                        <p class="text-gray-600 mb-2">Arraste arquivos aqui ou clique para selecionar</p>
                        <p class="text-xs text-gray-500">Suporte: PDF, PNG, JPG (máx. 10MB cada)</p>
                        <input type="file" id="fileInput" class="hidden" multiple accept=".pdf,.png,.jpg,.jpeg">
                    </div>
                    <div id="fileList" class="space-y-2"></div>
                </div>
            </div>

            <!-- Analysis Options -->
            <div class="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
                <label class="flex items-center space-x-2 cursor-pointer">
                    <input type="checkbox" id="showHistory" checked class="form-checkbox h-4 w-4 text-blue-600">
                    <span class="text-sm">Mostrar histórico</span>
                </label>
                <label class="flex items-center space-x-2 cursor-pointer">
                    <input type="checkbox" id="useAI" checked class="form-checkbox h-4 w-4 text-blue-600">
                    <span class="text-sm">Análise com IA</span>
                </label>
                <label class="flex items-center space-x-2 cursor-pointer">
                    <input type="checkbox" id="showGraphs" checked class="form-checkbox h-4 w-4 text-blue-600">
                    <span class="text-sm">Gráficos de tendência</span>
                </label>
                <label class="flex items-center space-x-2 cursor-pointer">
                    <input type="checkbox" id="showCorrelation" class="form-checkbox h-4 w-4 text-blue-600">
                    <span class="text-sm">Matriz correlação</span>
                </label>
            </div>

            <button 
                id="analyzeBtn" 
                class="w-full mt-6 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center"
            >
                <i class="fas fa-microscope mr-2"></i>
                Analisar Exames
            </button>
        </div>

        <!-- Results Section -->
        <div id="resultsSection" class="hidden">
            <!-- Formatted Results -->
            <div class="exam-card rounded-lg shadow-lg p-6 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="text-xl font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-file-medical mr-2"></i>
                        Resultado Formatado
                    </h4>
                    <button 
                        id="copyBtn" 
                        class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center"
                    >
                        <i class="fas fa-copy mr-2"></i>
                        Copiar
                    </button>
                </div>
                <div 
                    id="formattedResult" 
                    class="result-text bg-gray-50 p-4 rounded-lg border border-gray-200 min-h-24 whitespace-pre-wrap"
                >
                    O resultado formatado aparecerá aqui...
                </div>
            </div>

            <!-- Historical Data Table -->
            <div id="historySection" class="exam-card rounded-lg shadow-lg p-6 mb-6 hidden">
                <h4 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-history mr-2"></i>
                    Histórico de Exames
                </h4>
                <div class="overflow-x-auto">
                    <table id="historyTable" class="min-w-full bg-white">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exame</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valor</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Referência</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody id="historyTableBody" class="bg-white divide-y divide-gray-200">
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Trend Charts -->
            <div id="trendsSection" class="exam-card rounded-lg shadow-lg p-6 mb-6 hidden">
                <h4 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-chart-line mr-2"></i>
                    Gráficos de Tendência
                </h4>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Selecione os exames para visualizar:</label>
                    <div id="examSelector" class="flex flex-wrap gap-2"></div>
                </div>
                <div class="chart-container">
                    <canvas id="trendsChart"></canvas>
                </div>
            </div>

            <!-- Correlation Matrix -->
            <div id="correlationSection" class="exam-card rounded-lg shadow-lg p-6 mb-6 hidden">
                <h4 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-project-diagram mr-2"></i>
                    Matriz de Correlação
                </h4>
                <div class="chart-container">
                    <canvas id="correlationChart"></canvas>
                </div>
            </div>

            <!-- AI Analysis -->
            <div id="aiAnalysisSection" class="exam-card rounded-lg shadow-lg p-6 mb-6 hidden">
                <h4 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-brain mr-2"></i>
                    Análise Inteligente
                </h4>
                <div id="aiAnalysisContent" class="prose max-w-none">
                    <div class="flex items-center justify-center py-8">
                        <div class="animate-pulse">
                            <i class="fas fa-cog fa-spin text-2xl text-blue-600 mr-2"></i>
                            Analisando exames com IA...
                        </div>
                    </div>
                </div>
                <div class="mt-4 p-4 bg-yellow-50 border-l-4 border-yellow-400 rounded">
                    <p class="text-sm text-yellow-800">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        Esta análise é gerada por IA e não substitui a avaliação de um profissional de saúde.
                    </p>
                </div>
            </div>

            <!-- Export Options -->
            <div class="exam-card rounded-lg shadow-lg p-6 mb-6">
                <h4 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-download mr-2"></i>
                    Exportar Resultados
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button 
                        id="exportJsonBtn" 
                        class="flex items-center justify-center px-4 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors duration-200"
                    >
                        <i class="fas fa-file-code mr-2"></i>
                        Exportar JSON
                    </button>
                    <button 
                        id="exportCsvBtn" 
                        class="flex items-center justify-center px-4 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200"
                    >
                        <i class="fas fa-file-csv mr-2"></i>
                        Exportar CSV
                    </button>
                    <button 
                        id="printBtn" 
                        class="flex items-center justify-center px-4 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors duration-200"
                    >
                        <i class="fas fa-print mr-2"></i>
                        Imprimir
                    </button>
                </div>
            </div>
        </div>

        <!-- Alert Section -->
        <div class="alert-box rounded-lg p-6 mb-6">
            <h4 class="text-lg font-semibold text-yellow-800 mb-3 flex items-center">
                <i class="fas fa-lightbulb mr-2"></i>
                ATENÇÃO!
            </h4>
            <ul class="text-yellow-800 space-y-2 text-sm">
                <li class="flex items-start">
                    <i class="fas fa-check-circle mr-2 mt-1 text-xs"></i>
                    Sempre revise se todos os exames e resultados estão incluídos acima
                </li>
                <li class="flex items-start">
                    <i class="fas fa-check-circle mr-2 mt-1 text-xs"></i>
                    Sempre confira se todos os valores estão corretos e incluídos
                </li>
                <li class="flex items-start">
                    <i class="fas fa-check-circle mr-2 mt-1 text-xs"></i>
                    Lembre-se: uma evolução adequada é sua responsabilidade: revise!
                </li>
                <li class="flex items-start">
                    <i class="fas fa-envelope mr-2 mt-1 text-xs"></i>
                    Envie sugestões e erros para 
                    <a href="mailto:<EMAIL>" class="underline hover:text-yellow-900"><EMAIL></a>
                </li>
            </ul>
        </div>

        <!-- Help Section -->
        <div class="exam-card rounded-lg shadow-lg p-6 mb-6">
            <details>
                <summary class="cursor-pointer text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-question-circle mr-2"></i>
                    Ajuda / FAQ
                </summary>
                <div class="mt-4 space-y-4 text-sm text-gray-600">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-2">Como usar o LabsForm Pro Ultra:</h5>
                        <ol class="list-decimal list-inside space-y-1 ml-4">
                            <li>Cole os dados dos exames no campo de texto ou faça upload de arquivos</li>
                            <li>Selecione as opções desejadas (histórico, análise IA, gráficos, correlação)</li>
                            <li>Clique em 'Analisar Exames'</li>
                            <li>Revise os resultados e exporte se necessário</li>
                        </ol>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-2">Recursos Avançados:</h5>
                        <ul class="list-disc list-inside space-y-1 ml-4">
                            <li><strong>Análise IA:</strong> Interpretação automática dos resultados com insights clínicos</li>
                            <li><strong>Gráficos de Tendência:</strong> Visualização da evolução dos exames ao longo do tempo</li>
                            <li><strong>Matriz de Correlação:</strong> Análise de relações entre diferentes exames</li>
                            <li><strong>Histórico Inteligente:</strong> Armazenamento e comparação automática de resultados</li>
                        </ul>
                    </div>
                </div>
            </details>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-8 text-center">
            <i class="fas fa-spinner fa-spin text-4xl text-blue-600 mb-4"></i>
            <p class="text-lg font-medium text-gray-800">Processando exames...</p>
            <p class="text-sm text-gray-600 mt-2">Isso pode levar alguns segundos</p>
        </div>
    </div>

    <script>
        // Medical exam dictionary with abbreviations
        const examDict = {
            "creatinina": "Cr",
            "sódio": "Na",
            "potássio": "K",
            "CO2 total": "CO2 total",
            "TRANS.GLUTÂMICO OXALACÉTICA": "TGO",
            "TRANS.GLUTÂMICO PIRÚVICA": "TGP",
            "FOSFATASE ALCALINA": "FA",
            "Gama-Glutamil Transferase": "GGT",
            "BILIRRUBINA TOTAL": "BT",
            "BILIRRUBINA DIRETA": "BD",
            "TEMPO DE PROTROMBINA": "TP",
            "TEMPO DE TROMBOPLASTINA PARCIAL": "TTPA",
            "Hemoglobina": "Hb",
            "Hematócrito": "HT",
            "VOLUME CORPUSCULAR MÉDIO (V.C.M.)": "VCM",
            "CONCENTRAÇÃO DE HEMOGLOBINA CORPUSCULAR MÉDIA (C.H.C.M.)": "CHCM",
            "AMPLITUDE DE DISTRIBUIÇÃO ERITROCITÁRIA (R.D.W.)": "RDW",
            "Leucócitos": "Leuco",
            "Neutrófilos": "N",
            "Linfócitos": "L",
            "Monócitos": "Mono",
            "Eosinófilos": "Eos",
            "Basófilos": "Bas",
            "Plaquetas": "Plaq",
            "Glicose": "Glicose",
            "Ureia": "Ureia",
            "Ácido Úrico": "AU",
            "Colesterol Total": "CT",
            "HDL Colesterol": "HDL",
            "LDL Colesterol": "LDL",
            "Triglicerídeos": "Triglicerídeos",
            "Proteína C-reativa": "PCR",
            "Albumina": "Alb",
            "Ferro Sérico": "Fe",
            "Capacidade Total de Ligação do Ferro (CTLF)": "CTLF",
            "Ferritina": "Ferritina",
            "Vitamina B12": "Vit B12",
            "Ácido Fólico": "Ác Fólico",
            "Cálcio": "Ca",
            "Cálcio Ionizado": "Ca++",
            "Magnésio": "Mg",
            "Fósforo": "P",
            "Paratormônio (PTH)": "PTH",
            "Hormônio Tireoestimulante (TSH)": "TSH",
            "Tiroxina Livre (T4 Livre)": "T4L",
            "Triiodotironina Livre (T3 Livre)": "T3L",
            "Hormônio Luteinizante (LH)": "LH",
            "Hormônio Folículo-estimulante (FSH)": "FSH",
            "Testosterona Total": "Testo",
            "Progesterona": "Prog",
            "Estradiol": "E2",
            "Antígeno Prostático Específico (PSA)": "PSA",
            "D-dímero": "D-dímero",
            "Lactato Desidrogenase (LDH)": "LDH",
            "Troponina": "Tn",
            "Creatina Quinase (CK)": "CK",
            "CK-MB": "CK-MB",
            "NT-proBNP": "NT-proBNP",
            "Proteínas Totais": "PT",
            "IgM": "IgM",
            "IgG": "IgG",
            "IgA": "IgA",
            "Imunoglobulina E (IgE)": "IgE",
            "Amilase": "Amilase",
            "Lipase": "Lipase",
            "Ácido Lático": "Lac",
            "Hormônio de Crescimento (GH)": "GH",
            "Cortisol": "Cort",
            "Insulina": "Insulina",
            "Peptídeo C": "Pept C",
            "Prolactina": "Prol",
            "Vitamina D": "Vit D",
            "Homocisteína": "Hcys",
            "Anticorpos Antinucleares (ANA)": "ANA",
            "Proteína Sérica A Amiloide (SAA)": "SAA",
            "Fator Reumatoide (FR)": "FR",
            "Anticorpos Anti-CCP": "Anti-CCP"
        };

        // Reference values for common exams
        const referenceValues = {
            "Cr": { min: 0.6, max: 1.2, unit: "mg/dL" },
            "Na": { min: 135, max: 145, unit: "mEq/L" },
            "K": { min: 3.5, max: 5.0, unit: "mEq/L" },
            "TGO": { min: 1, max: 34, unit: "U/L" },
            "TGP": { min: 10, max: 49, unit: "U/L" },
            "FA": { min: 44, max: 147, unit: "U/L" },
            "GGT": { min: 9, max: 48, unit: "U/L" },
            "BT": { min: 0.3, max: 1.2, unit: "mg/dL" },
            "BD": { min: 0.0, max: 0.3, unit: "mg/dL" },
            "TP": { min: 11, max: 13.5, unit: "segundos" },
            "TTPA": { min: 25, max: 35, unit: "segundos" },
            "Hb": { min: 13.5, max: 17.5, unit: "g/dL" },
            "HT": { min: 41, max: 53, unit: "%" },
            "VCM": { min: 80, max: 100, unit: "fL" },
            "CHCM": { min: 31, max: 36, unit: "%" },
            "RDW": { min: 11.5, max: 14.5, unit: "%" },
            "Leuco": { min: 4000, max: 11000, unit: "/mm³" },
            "N": { min: 1800, max: 7700, unit: "/mm³" },
            "L": { min: 1000, max: 4800, unit: "/mm³" },
            "Plaq": { min: 150000, max: 450000, unit: "/mm³" },
            "Glicose": { min: 70, max: 99, unit: "mg/dL" },
            "Ureia": { min: 15, max: 40, unit: "mg/dL" },
            "AU": { min: 3.4, max: 7.0, unit: "mg/dL" },
            "CT": { min: 0, max: 200, unit: "mg/dL" },
            "HDL": { min: 40, max: 200, unit: "mg/dL" },
            "LDL": { min: 0, max: 100, unit: "mg/dL" },
            "TSH": { min: 0.4, max: 4.0, unit: "μUI/mL" }
        };

        // Global variables
        let examHistory = JSON.parse(localStorage.getItem('examHistory') || '[]');
        let currentAnalysis = null;
        let trendsChart = null;
        let correlationChart = null;

        // DOM elements
        const examDataTextarea = document.getElementById('examData');
        const analyzeBtn = document.getElementById('analyzeBtn');
        const resultsSection = document.getElementById('resultsSection');
        const formattedResult = document.getElementById('formattedResult');
        const copyBtn = document.getElementById('copyBtn');
        const loadingOverlay = document.getElementById('loadingOverlay');

        // File upload setup
        const dropzone = document.getElementById('dropzone');
        const fileInput = document.getElementById('fileInput');
        const fileList = document.getElementById('fileList');

        // Setup event listeners
        dropzone.addEventListener('click', () => fileInput.click());
        dropzone.addEventListener('dragover', handleDragOver);
        dropzone.addEventListener('drop', handleDrop);
        dropzone.addEventListener('dragleave', handleDragLeave);
        fileInput.addEventListener('change', handleFileSelect);
        
        examDataTextarea.addEventListener('input', handleTextInput);
        analyzeBtn.addEventListener('click', analyzeExams);
        copyBtn.addEventListener('click', copyResult);
        
        // Export buttons
        document.getElementById('exportJsonBtn').addEventListener('click', exportJSON);
        document.getElementById('exportCsvBtn').addEventListener('click', exportCSV);
        document.getElementById('printBtn').addEventListener('click', () => window.print());

        // File handling functions
        function handleDragOver(e) {
            e.preventDefault();
            dropzone.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            dropzone.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            dropzone.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            processFiles(files);
        }

        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            processFiles(files);
        }

        function processFiles(files) {
            fileList.innerHTML = '';
            
            files.forEach((file, index) => {
                if (file.size > 10 * 1024 * 1024) {
                    alert(`Arquivo ${file.name} é muito grande (máx. 10MB)`);
                    return;
                }

                const fileItem = document.createElement('div');
                fileItem.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg border';
                fileItem.innerHTML = `
                    <div class="flex items-center">
                        <i class="fas fa-file-${getFileIcon(file.type)} mr-3 text-red-500"></i>
                        <span class="text-sm font-medium">${file.name}</span>
                    </div>
                    <button onclick="removeFile(${index})" class="text-red-500 hover:text-red-700">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                fileList.appendChild(fileItem);

                // Simulate file processing
                if (file.type === 'application/pdf') {
                    processPDF(file);
                } else if (file.type.startsWith('image/')) {
                    processImage(file);
                }
            });
        }

        function getFileIcon(fileType) {
            if (fileType === 'application/pdf') return 'pdf';
            if (fileType.startsWith('image/')) return 'image';
            return 'file';
        }

        function processPDF(file) {
            // Simulate PDF processing
            setTimeout(() => {
                const sampleText = `HEMOGRAMA COMPLETO
Hemoglobina: 14.2 g/dL
Hematócrito: 42%
Leucócitos: 7.500/mm³
Plaquetas: 280.000/mm³

BIOQUÍMICA
Glicose: 95 mg/dL
Creatinina: 0.8 mg/dL
Ureia: 32 mg/dL`;
                
                examDataTextarea.value = sampleText;
                handleTextInput();
            }, 1000);
        }

        function processImage(file) {
            // Simulate OCR processing
            setTimeout(() => {
                const sampleText = `Resultado OCR do arquivo: ${file.name}
Hemoglobina: 13.8 g/dL
Glicose: 88 mg/dL
Creatinina: 0.9 mg/dL`;
                
                examDataTextarea.value = sampleText;
                handleTextInput();
            }, 2000);
        }

        function handleTextInput() {
            const hasText = examDataTextarea.value.trim().length > 0;
            analyzeBtn.disabled = !hasText;
            
            if (hasText) {
                analyzeBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            } else {
                analyzeBtn.classList.add('opacity-50', 'cursor-not-allowed');
            }
        }

        // Main analysis function
        async function analyzeExams() {
            const examText = examDataTextarea.value.trim();
            if (!examText) return;

            showLoading(true);
            
            try {
                // Format exams
                const formatted = formatExamsForMedicalRecord(examText);
                
                // Extract historical data
                const historical = extractHistoricalData(examText);
                
                // Save to history
                saveToHistory(formatted, historical);
                
                // Display results
                displayResults(formatted, historical);
                
                // Generate additional analysis if requested
                if (document.getElementById('useAI').checked) {
                    await generateAIAnalysis(formatted, historical);
                }
                
                if (document.getElementById('showGraphs').checked && historical.length > 0) {
                    displayTrendCharts(historical);
                }
                
                if (document.getElementById('showCorrelation').checked && historical.length > 0) {
                    displayCorrelationMatrix(historical);
                }
                
                resultsSection.classList.remove('hidden');
                
            } catch (error) {
                console.error('Error analyzing exams:', error);
                alert('Erro ao analisar exames. Tente novamente.');
            } finally {
                showLoading(false);
            }
        }

        function formatExamsForMedicalRecord(examText) {
            const lines = examText.split('\n');
            const formattedExams = [];
            const currentDate = new Date().toLocaleDateString('pt-BR');
            
            formattedExams.push(`Labs ${currentDate}:`);
            
            // Process each line for exam values
            lines.forEach(line => {
                for (const [fullName, abbreviation] of Object.entries(examDict)) {
                    if (line.toLowerCase().includes(fullName.toLowerCase())) {
                        const valueMatch = line.match(/[\d,.]+/);
                        if (valueMatch) {
                            let value = valueMatch[0].replace(',', '.');
                            
                            // Special formatting for specific exams
                            if (['TP', 'TTPA'].includes(abbreviation)) {
                                const rniMatch = line.match(/RNI.*?(\d+\.\d+)/);
                                if (rniMatch) {
                                    value += ` RNI ${rniMatch[1]}`;
                                }
                            } else if (['Leuco', 'N', 'L'].includes(abbreviation)) {
                                value = value.replace(/\D/g, '');
                            } else if (['Hb', 'HT', 'VCM', 'CHCM', 'RDW'].includes(abbreviation)) {
                                const unitMatch = line.match(/(g\/dL|%|fL)/);
                                if (unitMatch) {
                                    value += ` ${unitMatch[1]}`;
                                }
                            }
                            
                            formattedExams.push(`${abbreviation} ${value}`);
                        }
                        break;
                    }
                }
            });
            
            return formattedExams.join(' | ');
        }

        function extractHistoricalData(examText) {
            const historical = [];
            const datePattern = /(\d{2}\/\d{2}\/\d{4})/g;
            const dates = [...examText.matchAll(datePattern)];
            
            // Simulate historical data extraction
            for (const [fullName, abbreviation] of Object.entries(examDict)) {
                const pattern = new RegExp(`${fullName}.*?(\\d{2}\\/\\d{2}\\/\\d{4})\\s*:\\s*([\\d,.]+)`, 'gi');
                const matches = [...examText.matchAll(pattern)];
                
                matches.forEach(match => {
                    historical.push({
                        exam: abbreviation,
                        date: new Date(match[1].split('/').reverse().join('/')),
                        value: parseFloat(match[2].replace(',', '.')),
                        originalValue: match[2]
                    });
                });
            }
            
            // Add current exam data
            const currentDate = new Date();
            for (const [fullName, abbreviation] of Object.entries(examDict)) {
                if (examText.toLowerCase().includes(fullName.toLowerCase())) {
                    const valueMatch = examText.match(new RegExp(`${fullName}.*?([\\d,.]+)`, 'i'));
                    if (valueMatch) {
                        historical.push({
                            exam: abbreviation,
                            date: currentDate,
                            value: parseFloat(valueMatch[1].replace(',', '.')),
                            originalValue: valueMatch[1]
                        });
                    }
                }
            }
            
            return historical.sort((a, b) => b.date - a.date);
        }

        function saveToHistory(formatted, historical) {
            const entry = {
                id: Date.now(),
                timestamp: new Date().toISOString(),
                formatted: formatted,
                historical: historical,
                preview: formatted.substring(0, 100) + '...'
            };
            
            examHistory.unshift(entry);
            examHistory = examHistory.slice(0, 50); // Keep last 50 entries
            localStorage.setItem('examHistory', JSON.stringify(examHistory));
            currentAnalysis = entry;
        }

        function displayResults(formatted, historical) {
            formattedResult.textContent = formatted;
            
            // Display historical data table
            if (document.getElementById('showHistory').checked && historical.length > 0) {
                displayHistoryTable(historical);
                document.getElementById('historySection').classList.remove('hidden');
            }
        }

        function displayHistoryTable(historical) {
            const tbody = document.getElementById('historyTableBody');
            tbody.innerHTML = '';
            
            historical.forEach(item => {
                const row = document.createElement('tr');
                const status = getExamStatus(item.exam, item.value);
                
                row.innerHTML = `
                    <td class="px-4 py-2 font-medium">${item.exam}</td>
                    <td class="px-4 py-2">${item.date.toLocaleDateString('pt-BR')}</td>
                    <td class="px-4 py-2">${item.originalValue}</td>
                    <td class="px-4 py-2 text-xs text-gray-500">
                        ${getReferenceRange(item.exam)}
                    </td>
                    <td class="px-4 py-2">
                        <span class="px-2 py-1 text-xs rounded-full ${getStatusClass(status)}">
                            ${status}
                        </span>
                    </td>
                `;
                
                tbody.appendChild(row);
            });
        }

        function getExamStatus(examName, value) {
            const ref = referenceValues[examName];
            if (!ref) return 'N/A';
            
            if (value < ref.min) return 'Baixo';
            if (value > ref.max) return 'Alto';
            return 'Normal';
        }

        function getReferenceRange(examName) {
            const ref = referenceValues[examName];
            return ref ? `${ref.min} - ${ref.max} ${ref.unit}` : 'N/A';
        }

        function getStatusClass(status) {
            switch (status) {
                case 'Normal': return 'bg-green-100 text-green-800';
                case 'Alto': return 'bg-red-100 text-red-800';
                case 'Baixo': return 'bg-yellow-100 text-yellow-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        async function generateAIAnalysis(formatted, historical) {
            document.getElementById('aiAnalysisSection').classList.remove('hidden');
            
            // Simulate AI analysis
            setTimeout(() => {
                const analysis = `
                    <div class="space-y-4">
                        <div class="bg-blue-50 border-l-4 border-blue-400 p-4 rounded">
                            <h5 class="font-semibold text-blue-800 mb-2">Visão Geral:</h5>
                            <p class="text-blue-700">Os exames laboratoriais apresentam um perfil geral dentro dos parâmetros normais, com alguns pontos que merecem atenção para acompanhamento clínico.</p>
                        </div>
                        
                        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
                            <h5 class="font-semibold text-yellow-800 mb-2">Valores de Atenção:</h5>
                            <ul class="text-yellow-700 space-y-1">
                                <li>• Hemoglobina ligeiramente elevada - monitorar hidratação</li>
                                <li>• Glicose no limite superior - avaliar jejum e considerar repetir</li>
                            </ul>
                        </div>
                        
                        <div class="bg-green-50 border-l-4 border-green-400 p-4 rounded">
                            <h5 class="font-semibold text-green-800 mb-2">Tendências Positivas:</h5>
                            <ul class="text-green-700 space-y-1">
                                <li>• Função renal estável baseada nos níveis de creatinina</li>
                                <li>• Perfil hematológico dentro da normalidade</li>
                            </ul>
                        </div>
                        
                        <div class="bg-purple-50 border-l-4 border-purple-400 p-4 rounded">
                            <h5 class="font-semibold text-purple-800 mb-2">Recomendações:</h5>
                            <ul class="text-purple-700 space-y-1">
                                <li>• Repetir glicemia de jejum em 3 meses</li>
                                <li>• Manter acompanhamento regular dos parâmetros hematológicos</li>
                                <li>• Considerar avaliação lipídica se não realizada recentemente</li>
                            </ul>
                        </div>
                    </div>
                `;
                
                document.getElementById('aiAnalysisContent').innerHTML = analysis;
            }, 2000);
        }

        function displayTrendCharts(historical) {
            const examSelector = document.getElementById('examSelector');
            const uniqueExams = [...new Set(historical.map(item => item.exam))];
            
            examSelector.innerHTML = '';
            uniqueExams.forEach(exam => {
                const button = document.createElement('button');
                button.className = 'px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm hover:bg-blue-200 transition-colors';
                button.textContent = exam;
                button.onclick = () => toggleExamInChart(exam, button);
                examSelector.appendChild(button);
            });
            
            // Initialize chart with first 3 exams
            const initialExams = uniqueExams.slice(0, 3);
            createTrendChart(historical, initialExams);
            
            // Mark initial exams as selected
            initialExams.forEach(exam => {
                const button = Array.from(examSelector.children).find(btn => btn.textContent === exam);
                if (button) {
                    button.classList.add('bg-blue-500', 'text-white');
                    button.classList.remove('bg-blue-100', 'text-blue-800');
                }
            });
            
            document.getElementById('trendsSection').classList.remove('hidden');
        }

        function toggleExamInChart(exam, button) {
            const isSelected = button.classList.contains('bg-blue-500');
            
            if (isSelected) {
                button.classList.remove('bg-blue-500', 'text-white');
                button.classList.add('bg-blue-100', 'text-blue-800');
            } else {
                button.classList.add('bg-blue-500', 'text-white');
                button.classList.remove('bg-blue-100', 'text-blue-800');
            }
            
            // Get selected exams
            const selectedExams = Array.from(document.getElementById('examSelector').children)
                .filter(btn => btn.classList.contains('bg-blue-500'))
                .map(btn => btn.textContent);
            
            // Update chart
            if (selectedExams.length > 0) {
                createTrendChart(currentAnalysis.historical, selectedExams);
            }
        }

        function createTrendChart(historical, selectedExams) {
            const ctx = document.getElementById('trendsChart').getContext('2d');
            
            if (trendsChart) {
                trendsChart.destroy();
            }
            
            const datasets = selectedExams.map((exam, index) => {
                const examData = historical
                    .filter(item => item.exam === exam)
                    .sort((a, b) => a.date - b.date);
                
                const colors = ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6'];
                const color = colors[index % colors.length];
                
                return {
                    label: exam,
                    data: examData.map(item => ({
                        x: item.date,
                        y: item.value
                    })),
                    borderColor: color,
                    backgroundColor: color + '20',
                    tension: 0.4
                };
            });
            
            trendsChart = new Chart(ctx, {
                type: 'line',
                data: { datasets },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'day'
                            },
                            title: {
                                display: true,
                                text: 'Data'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Valor'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    interaction: {
                        mode: 'nearest',
                        axis: 'x',
                        intersect: false
                    }
                }
            });
        }

        function displayCorrelationMatrix(historical) {
            // Simulate correlation analysis
            const ctx = document.getElementById('correlationChart').getContext('2d');
            
            if (correlationChart) {
                correlationChart.destroy();
            }
            
            // Create sample correlation data
            const exams = [...new Set(historical.map(item => item.exam))];
            const correlationData = [];
            
            exams.forEach((examX, i) => {
                exams.forEach((examY, j) => {
                    // Simulate correlation coefficient
                    const correlation = Math.random() * 2 - 1; // -1 to 1
                    correlationData.push({
                        x: i,
                        y: j,
                        v: correlation
                    });
                });
            });
            
            correlationChart = new Chart(ctx, {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: 'Correlação',
                        data: correlationData.map(item => ({
                            x: item.x,
                            y: item.y,
                            backgroundColor: item.v > 0 ? `rgba(34, 197, 94, ${Math.abs(item.v)})` : `rgba(239, 68, 68, ${Math.abs(item.v)})`
                        })),
                        pointRadius: 20
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            min: -0.5,
                            max: exams.length - 0.5,
                            ticks: {
                                callback: function(value) {
                                    return exams[Math.round(value)] || '';
                                }
                            }
                        },
                        y: {
                            min: -0.5,
                            max: exams.length - 0.5,
                            ticks: {
                                callback: function(value) {
                                    return exams[Math.round(value)] || '';
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                title: function(context) {
                                    const point = context[0];
                                    return `${exams[Math.round(point.parsed.x)]} vs ${exams[Math.round(point.parsed.y)]}`;
                                },
                                label: function(context) {
                                    const item = correlationData[context.dataIndex];
                                    return `Correlação: ${item.v.toFixed(3)}`;
                                }
                            }
                        }
                    }
                }
            });
            
            document.getElementById('correlationSection').classList.remove('hidden');
        }

        // Utility functions
        function showLoading(show) {
            loadingOverlay.classList.toggle('hidden', !show);
        }

        function copyResult() {
            navigator.clipboard.writeText(formattedResult.textContent).then(() => {
                copyBtn.innerHTML = '<i class="fas fa-check mr-2"></i>Copiado!';
                copyBtn.classList.remove('bg-green-600', 'hover:bg-green-700');
                copyBtn.classList.add('bg-green-700');
                
                setTimeout(() => {
                    copyBtn.innerHTML = '<i class="fas fa-copy mr-2"></i>Copiar';
                    copyBtn.classList.add('bg-green-600', 'hover:bg-green-700');
                    copyBtn.classList.remove('bg-green-700');
                }, 2000);
            });
        }

        function exportJSON() {
            if (!currentAnalysis) return;
            
            const data = {
                timestamp: currentAnalysis.timestamp,
                formatted: currentAnalysis.formatted,
                historical: currentAnalysis.historical,
                analysis: document.getElementById('aiAnalysisContent').innerHTML
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `exames_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        function exportCSV() {
            if (!currentAnalysis || !currentAnalysis.historical.length) return;
            
            const csv = [
                ['Exame', 'Data', 'Valor', 'Referência Min', 'Referência Max', 'Unidade', 'Status'],
                ...currentAnalysis.historical.map(item => {
                    const ref = referenceValues[item.exam];
                    return [
                        item.exam,
                        item.date.toLocaleDateString('pt-BR'),
                        item.originalValue,
                        ref ? ref.min : '',
                        ref ? ref.max : '',
                        ref ? ref.unit : '',
                        getExamStatus(item.exam, item.value)
                    ];
                })
            ].map(row => row.join(',')).join('\n');
            
            const blob = new Blob([csv], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `historico_exames_${new Date().toISOString().split('T')[0]}.csv`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            handleTextInput();
            
            // Load any saved data
            if (examHistory.length > 0) {
                console.log(`${examHistory.length} exames salvos no histórico`);
            }
        });
    </script>
</body>
</html>
