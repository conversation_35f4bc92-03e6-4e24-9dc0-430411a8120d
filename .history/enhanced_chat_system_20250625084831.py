import streamlit as st
from anthropic import Anthropic
import os
import json
from datetime import datetime
import re
from typing import Dict, List, Tuple, Optional
import pandas as pd
import numpy as np
from dataclasses import dataclass
from enum import Enum
import nltk
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.corpus import stopwords
from nltk.stem import WordNetLemmatizer
import spacy
from textblob import TextBlob
from collections import defaultdict, Counter
import pickle
from utils import MedicalDatabase, VitalSignsAnalyzer, SymptomAnalyzer

# Download required NLTK data
try:
    nltk.download('punkt', quiet=True)
    nltk.download('stopwords', quiet=True)
    nltk.download('wordnet', quiet=True)
    nltk.download('averaged_perceptron_tagger', quiet=True)
except:
    pass

# Load spaCy model
try:
    nlp = spacy.load("en_core_web_sm")
except:
    os.system("python -m spacy download en_core_web_sm")
    nlp = spacy.load("en_core_web_sm")

class ConversationContext(Enum):
    """Types of conversation contexts"""
    SYMPTOMS = "symptoms"
    DIAGNOSIS = "diagnosis"
    TREATMENT = "treatment"
    PREVENTION = "prevention"
    MEDICATION = "medication"
    EMERGENCY = "emergency"
    GENERAL_HEALTH = "general_health"
    MENTAL_HEALTH = "mental_health"
    NUTRITION = "nutrition"
    EXERCISE = "exercise"
    LAB_RESULTS = "lab_results"
    FOLLOW_UP = "follow_up"
    MEDICAL_HISTORY = "medical_history"
    ALLERGIES = "allergies"
    LIFESTYLE = "lifestyle"

@dataclass
class ConversationState:
    """Maintains conversation state and context"""
    context: ConversationContext
    entities: Dict[str, List[str]]
    sentiment: float
    urgency_level: str
    topics_discussed: List[str]
    pending_questions: List[str]
    patient_concerns: List[str]
    
class EnhancedMedicalChatSystem:
    def __init__(self):
        self.anthropic = Anthropic(api_key=os.getenv("ANTHROPIC_API_KEY"))
        self.conversation_memory = defaultdict(list)
        self.medical_knowledge_base = self._load_medical_knowledge()
        self.context_analyzer = ContextAnalyzer()
        self.response_generator = ResponseGenerator()
        self.entity_extractor = MedicalEntityExtractor()
        
    def _load_medical_knowledge(self):
        """Load comprehensive medical knowledge base"""
        return {
            "symptoms": {
                "respiratory": ["cough", "shortness of breath", "wheezing", "chest tightness", "sputum"],
                "cardiovascular": ["chest pain", "palpitations", "edema", "syncope", "dyspnea"],
                "gastrointestinal": ["nausea", "vomiting", "diarrhea", "constipation", "abdominal pain"],
                "neurological": ["headache", "dizziness", "seizures", "weakness", "numbness"],
                "psychiatric": ["anxiety", "depression", "insomnia", "mood changes", "hallucinations"],
                "musculoskeletal": ["joint pain", "muscle pain", "stiffness", "swelling", "limited mobility"],
                "dermatological": ["rash", "itching", "lesions", "discoloration", "dryness"],
                "constitutional": ["fever", "fatigue", "weight loss", "night sweats", "malaise"]
            },
            "emergency_keywords": [
                "chest pain", "difficulty breathing", "severe bleeding", "unconscious",
                "stroke", "heart attack", "severe pain", "suicidal", "overdose",
                "severe allergic reaction", "seizure", "severe trauma"
            ],
            "medical_abbreviations": {
                "BP": "blood pressure",
                "HR": "heart rate",
                "RR": "respiratory rate",
                "T": "temperature",
                "O2": "oxygen saturation",
                "CBC": "complete blood count",
                "ECG": "electrocardiogram",
                "MRI": "magnetic resonance imaging",
                "CT": "computed tomography",
                "UTI": "urinary tract infection",
                "COPD": "chronic obstructive pulmonary disease",
                "DM": "diabetes mellitus",
                "HTN": "hypertension"
            },
            "treatment_modalities": {
                "pharmacological": ["antibiotics", "analgesics", "anti-inflammatory", "antihistamines"],
                "non_pharmacological": ["rest", "hydration", "physical therapy", "counseling"],
                "interventional": ["surgery", "catheterization", "biopsy", "drainage"],
                "preventive": ["vaccination", "screening", "lifestyle modification", "prophylaxis"]
            }
        }
    
    def process_message(self, user_input: str, patient_info: Dict = None) -> Dict:
        """Process user message with enhanced understanding"""
        # Extract entities and analyze context
        entities = self.entity_extractor.extract(user_input)
        context = self.context_analyzer.analyze(user_input, entities)
        sentiment = self._analyze_sentiment(user_input)
        urgency = self._assess_urgency(user_input, entities)
        
        # Update conversation state
        state = ConversationState(
            context=context,
            entities=entities,
            sentiment=sentiment,
            urgency_level=urgency,
            topics_discussed=self._extract_topics(user_input),
            pending_questions=self._generate_followup_questions(context, entities),
            patient_concerns=self._identify_concerns(user_input)
        )
        
        # Generate appropriate response
        response = self._generate_enhanced_response(user_input, state, patient_info)
        
        # Store in conversation memory
        self.conversation_memory[datetime.now()] = {
            'user_input': user_input,
            'state': state,
            'response': response
        }
        
        return {
            'response': response,
            'context': context.value,
            'urgency': urgency,
            'entities': entities,
            'follow_up_questions': state.pending_questions,
            'recommendations': self._generate_recommendations(state)
        }
    
    def _analyze_sentiment(self, text: str) -> float:
        """Analyze emotional sentiment of the text"""
        blob = TextBlob(text)
        return blob.sentiment.polarity
    
    def _assess_urgency(self, text: str, entities: Dict) -> str:
        """Assess medical urgency level"""
        text_lower = text.lower()
        
        # Check for emergency keywords
        for keyword in self.medical_knowledge_base['emergency_keywords']:
            if keyword in text_lower:
                return "EMERGENCY"
        
        # Check severity indicators
        severity_indicators = ["severe", "extreme", "unbearable", "worst", "terrible"]
        if any(indicator in text_lower for indicator in severity_indicators):
            return "HIGH"
        
        # Check duration indicators
        if re.search(r'\b(weeks|months|years)\b', text_lower):
            return "MODERATE"
        
        return "ROUTINE"
    
    def _extract_topics(self, text: str) -> List[str]:
        """Extract medical topics from text"""
        doc = nlp(text)
        topics = []
        
        # Extract noun phrases
        for chunk in doc.noun_chunks:
            if any(token.pos_ in ['NOUN', 'PROPN'] for token in chunk):
                topics.append(chunk.text.lower())
        
        # Match against medical categories
        for category, terms in self.medical_knowledge_base['symptoms'].items():
            if any(term in text.lower() for term in terms):
                topics.append(category)
        
        return list(set(topics))
    
    def _generate_followup_questions(self, context: ConversationContext, entities: Dict) -> List[str]:
        """Generate intelligent follow-up questions"""
        questions = []
        
        if context == ConversationContext.SYMPTOMS:
            questions.extend([
                "How long have you been experiencing these symptoms?",
                "Have the symptoms been getting worse, better, or staying the same?",
                "Are there any triggers that make the symptoms worse?",
                "Have you tried any treatments or medications?"
            ])
        
        elif context == ConversationContext.MEDICATION:
            questions.extend([
                "What medications are you currently taking?",
                "Have you experienced any side effects?",
                "Are you taking the medication as prescribed?",
                "Do you have any known drug allergies?"
            ])
        
        elif context == ConversationContext.MENTAL_HEALTH:
            questions.extend([
                "How has your mood been recently?",
                "Are you getting adequate sleep?",
                "Have there been any significant life changes or stressors?",
                "Would you like to discuss coping strategies?"
            ])
        
        # Add entity-specific questions
        if 'symptoms' in entities and entities['symptoms']:
            symptom = entities['symptoms'][0]
            questions.append(f"Can you describe the {symptom} in more detail?")
        
        return questions[:3]  # Return top 3 most relevant questions
    
    def _identify_concerns(self, text: str) -> List[str]:
        """Identify patient's main concerns"""
        concerns = []
        
        # Common concern patterns
        concern_patterns = [
            r"worried about (.+)",
            r"concerned about (.+)",
            r"afraid of (.+)",
            r"scared that (.+)",
            r"anxious about (.+)"
        ]
        
        for pattern in concern_patterns:
            matches = re.findall(pattern, text.lower())
            concerns.extend(matches)
        
        # Extract questions as concerns
        if "?" in text:
            questions = text.split("?")
            concerns.extend([q.strip() for q in questions if q.strip()])
        
        return concerns
    
    def _generate_enhanced_response(self, user_input: str, state: ConversationState, patient_info: Dict) -> str:
        """Generate comprehensive, context-aware response"""
        
        # Build enhanced prompt
        prompt = f"""
You are an advanced medical AI assistant with comprehensive medical knowledge. 
Provide a detailed, empathetic, and medically accurate response.

Patient Message: {user_input}
Detected Context: {state.context.value}
Urgency Level: {state.urgency_level}
Sentiment: {'Positive' if state.sentiment > 0 else 'Negative' if state.sentiment < 0 else 'Neutral'}
Main Concerns: {', '.join(state.patient_concerns) if state.patient_concerns else 'None identified'}

Patient Information:
{json.dumps(patient_info, indent=2) if patient_info else 'No patient information provided'}

Previous Topics Discussed: {', '.join(state.topics_discussed)}

Please provide a response that:
1. Addresses all patient concerns comprehensively
2. Uses appropriate medical terminology while remaining accessible
3. Shows empathy and understanding
4. Provides specific, actionable advice when appropriate
5. Includes relevant medical information and education
6. Suggests appropriate follow-up actions
7. Maintains a professional yet caring tone

If this is an emergency situation, prioritize immediate safety recommendations.

Format your response with clear sections:
📋 ASSESSMENT
🔍 DETAILED EXPLANATION  
💊 RECOMMENDATIONS
⚠️ IMPORTANT CONSIDERATIONS
📅 FOLLOW-UP ACTIONS
"""
        
        try:
            response = self.anthropic.messages.create(
                model="claude-sonnet-4-20250514",
                max_tokens=64000,
                temperature=0.1,
                system="You are a knowledgeable, empathetic medical AI assistant. Provide comprehensive, accurate medical information while maintaining a caring and professional tone.",
                messages=[{"role": "user", "content": prompt}]
            )
            return response.content[0].text
        except Exception as e:
            return self._generate_fallback_response(state)
    
    def _generate_fallback_response(self, state: ConversationState) -> str:
        """Generate fallback response if API fails"""
        if state.urgency_level == "EMERGENCY":
            return """
🚨 EMERGENCY DETECTED

Based on your symptoms, this may be a medical emergency.

IMMEDIATE ACTIONS:
1. Call emergency services (911) immediately
2. Do not drive yourself to the hospital
3. If possible, have someone stay with you
4. Remain calm and follow emergency operator instructions

If you're experiencing:
- Chest pain or pressure
- Difficulty breathing
- Severe bleeding
- Loss of consciousness
- Signs of stroke (FAST: Face drooping, Arm weakness, Speech difficulty, Time to call 911)

Please seek immediate medical attention. Your safety is the top priority.
"""
        
        return f"""
Thank you for sharing your concerns. Based on what you've told me, I understand you're experiencing {', '.join(state.topics_discussed)}.

While I cannot provide a definitive diagnosis, I recommend:
1. Documenting your symptoms, including when they occur and their severity
2. Scheduling an appointment with your healthcare provider
3. Seeking immediate care if symptoms worsen

Please let me know if you have any specific questions or if there's anything else concerning you.
"""
    
    def _generate_recommendations(self, state: ConversationState) -> List[str]:
        """Generate context-specific recommendations"""
        recommendations = []
        
        if state.context == ConversationContext.SYMPTOMS:
            recommendations.extend([
                "Keep a symptom diary noting triggers and patterns",
                "Monitor vital signs if possible",
                "Stay hydrated and get adequate rest"
            ])
        
        elif state.context == ConversationContext.MENTAL_HEALTH:
            recommendations.extend([
                "Practice stress-reduction techniques like deep breathing",
                "Maintain regular sleep schedule",
                "Consider speaking with a mental health professional"
            ])
        
        elif state.context == ConversationContext.NUTRITION:
            recommendations.extend([
                "Maintain a balanced diet with variety",
                "Stay adequately hydrated",
                "Consider consulting with a registered dietitian"
            ])
        
        # Add urgency-based recommendations
        if state.urgency_level == "HIGH":
            recommendations.insert(0, "Schedule an appointment with your healthcare provider soon")
        elif state.urgency_level == "EMERGENCY":
            recommendations.insert(0, "Seek immediate medical attention")
        
        return recommendations

class ContextAnalyzer:
    """Analyzes conversation context using NLP"""
    
    def __init__(self):
        self.context_keywords = {
            ConversationContext.SYMPTOMS: ["pain", "hurt", "ache", "fever", "cough", "symptom", "feeling"],
            ConversationContext.DIAGNOSIS: ["what is", "do i have", "could it be", "diagnosis", "condition"],
            ConversationContext.TREATMENT: ["treatment", "cure", "medicine", "therapy", "help with"],
            ConversationContext.MEDICATION: ["medication", "drug", "pill", "prescription", "dose"],
            ConversationContext.MENTAL_HEALTH: ["anxiety", "depression", "stress", "mood", "mental", "emotional"],
            ConversationContext.NUTRITION: ["diet", "food", "nutrition", "eating", "weight"],
            ConversationContext.EXERCISE: ["exercise", "workout", "physical activity", "fitness"],
            ConversationContext.PREVENTION: ["prevent", "avoid", "risk", "protection", "vaccine"]
        }
    
    def analyze(self, text: str, entities: Dict) -> ConversationContext:
        """Determine conversation context"""
        text_lower = text.lower()
        context_scores = defaultdict(int)
        
        # Score based on keyword matches
        for context, keywords in self.context_keywords.items():
            for keyword in keywords:
                if keyword in text_lower:
                    context_scores[context] += 1
        
        # Boost scores based on entities
        if entities.get('symptoms'):
            context_scores[ConversationContext.SYMPTOMS] += 2
        if entities.get('medications'):
            context_scores[ConversationContext.MEDICATION] += 2
        
        # Return context with highest score
        if context_scores:
            return max(context_scores.items(), key=lambda x: x[1])[0]
        
        return ConversationContext.GENERAL_HEALTH

class MedicalEntityExtractor:
    """Extracts medical entities from text"""
    
    def __init__(self):
        self.symptom_patterns = [
            r"(?:experiencing|having|feeling|suffer from) (\w+)",
            r"(\w+ pain)",
            r"(\w+ ache)",
            r"(fever|cough|nausea|dizziness|fatigue)"
        ]
        
        self.medication_patterns = [
            r"taking (\w+)",
            r"prescribed (\w+)",
            r"(\w+) \d+mg",
            r"medication called (\w+)"
        ]
    
    def extract(self, text: str) -> Dict[str, List[str]]:
        """Extract medical entities from text"""
        entities = {
            'symptoms': [],
            'medications': [],
            'body_parts': [],
            'conditions': [],
            'lab_values': [],
            'time_expressions': []
        }
        
        # Extract symptoms
        for pattern in self.symptom_patterns:
            matches = re.findall(pattern, text.lower())
            entities['symptoms'].extend(matches)
        
        # Extract medications
        for pattern in self.medication_patterns:
            matches = re.findall(pattern, text.lower())
            entities['medications'].extend(matches)
        
        # Use spaCy for additional entity extraction
        doc = nlp(text)
        for ent in doc.ents:
            if ent.label_ == "DATE":
                entities['time_expressions'].append(ent.text)
            elif ent.label_ == "PERSON":
                # Could be doctor names
                pass
        
        # Extract body parts
        body_parts = ["head", "chest", "stomach", "back", "leg", "arm", "throat", "ear", "eye"]
        for part in body_parts:
            if part in text.lower():
                entities['body_parts'].append(part)
        
        # Extract lab values (simplified)
        lab_pattern = r"(\w+)\s*[:=]\s*(\d+\.?\d*)\s*(\w+)?"
        lab_matches = re.findall(lab_pattern, text)
        for match in lab_matches:
            entities['lab_values'].append({
                'test': match[0],
                'value': match[1],
                'unit': match[2] if match[2] else ''
            })
        
        # Clean up duplicates
        for key in entities:
            if isinstance(entities[key], list) and all(isinstance(item, str) for item in entities[key]):
                entities[key] = list(set(entities[key]))
        
        return entities

class ResponseGenerator:
    """Generates natural, empathetic responses"""
    
    def __init__(self):
        self.empathy_phrases = [
            "I understand this must be concerning for you",
            "I can see why you'd be worried about this",
            "Thank you for sharing this with me",
            "I appreciate you providing these details",
            "Your health concerns are valid and important"
        ]
        
        self.reassurance_phrases = [
            "You're taking the right step by seeking information",
            "Many people experience similar symptoms",
            "There are effective treatments available",
            "With proper care, most conditions improve",
            "Your healthcare provider can help determine the best course of action"
        ]
    
    def add_empathy(self, response: str, sentiment: float) -> str:
        """Add empathetic elements to response"""
        if sentiment < -0.3:
            # Patient seems distressed
            empathy = np.random.choice(self.empathy_phrases)
            response = f"{empathy}. {response}"
        
        return response
    
    def add_reassurance(self, response: str, urgency: str) -> str:
        """Add reassurance when appropriate"""
        if urgency not in ["EMERGENCY", "HIGH"]:
            reassurance = np.random.choice(self.reassurance_phrases)
            response = f"{response}\n\n{reassurance}."
        
        return response

# Streamlit Enhanced Chat Interface
def create_enhanced_chat_interface():
    st.markdown("""
    <style>
        /* Enhanced Chat Styling */
        .chat-container {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 20px;
            padding: 2rem;
            margin: 1rem 0;
        }
        
        .enhanced-message {
            background: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
            animation: slideIn 0.3s ease-out;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .context-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .urgency-emergency {
            background: #ff4757;
            color: white;
        }
        
        .urgency-high {
            background: #ff6348;
            color: white;
        }
        
        .urgency-moderate {
            background: #ffa502;
            color: white;
        }
        
        .urgency-routine {
            background: #2ed573;
            color: white;
        }
        
        .entity-tag {
            display: inline-block;
            background: #e3f2fd;
            color: #1976d2;
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
            margin: 0.2rem;
            font-size: 0.85rem;
        }
        
        .follow-up-questions {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 10px 10px 0;
        }
        
        .recommendation-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin: 0.5rem 0;
        }
        
        .typing-indicator {
            display: flex;
            align-items: center;
            padding: 1rem;
        }
        
        .typing-dot {
            width: 8px;
            height: 8px;
            background: #666;
            border-radius: 50%;
            margin: 0 2px;
            animation: typing 1.4s infinite;
        }
        
        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }
        
        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }
        
        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }
        
        .smart-suggestions {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin: 1rem 0;
        }
        
        .suggestion-pill {
            background: #e8f4f8;
            color: #0066cc;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 0.9rem;
        }
        
        .suggestion-pill:hover {
            background: #0066cc;
            color: white;
            transform: translateY(-2px);
        }
    </style>
    """, unsafe_allow_html=True)

def main():
    st.set_page_config(
        page_title="MediBot AI - Enhanced Chat System",
        page_icon="💬",
        layout="wide"
    )
    
    # Initialize enhanced chat system
    if 'chat_system' not in st.session_state:
        st.session_state.chat_system = EnhancedMedicalChatSystem()
    if 'enhanced_conversation' not in st.session_state:
        st.session_state.enhanced_conversation = []
    if 'current_context' not in st.session_state:
        st.session_state.current_context = ConversationContext.GENERAL_HEALTH
    
    # Apply enhanced styling
    create_enhanced_chat_interface()
    
    # Header
    st.markdown("""
    <div class="chat-container">
        <h1 style="text-align: center; color: #2c3e50;">
            💬 Enhanced Medical Chat Assistant
        </h1>
        <p style="text-align: center; color: #7f8c8d;">
            Comprehensive medical consultation with advanced understanding
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # Sidebar with chat insights
    with st.sidebar:
        st.markdown("### 📊 Conversation Insights")
        
        if st.session_state.enhanced_conversation:
            # Show current context
            st.markdown(f"""
            <div class="context-badge" style="background: #0066cc; color: white;">
                Current Context: {st.session_state.current_context.value.replace('_', ' ').title()}
            </div>
            """, unsafe_allow_html=True)
            
            # Show conversation statistics
            total_messages = len(st.session_state.enhanced_conversation)
            user_messages = sum(1 for msg in st.session_state.enhanced_conversation if msg['role'] == 'user')
            
            col1, col2 = st.columns(2)
            with col1:
                st.metric("Total Messages", total_messages)
            with col2:
                st.metric("Your Messages", user_messages)
            
            # Topics discussed
            st.markdown("### 🏷️ Topics Discussed")
            all_topics = []
            for msg in st.session_state.enhanced_conversation:
                if msg['role'] == 'assistant' and 'metadata' in msg:
                    all_topics.extend(msg['metadata'].get('topics', []))
            
            unique_topics = list(set(all_topics))[:5]
            for topic in unique_topics:
                st.markdown(f"""
                <span class="entity-tag">{topic}</span>
                """, unsafe_allow_html=True)
        
        # Quick actions
        st.markdown("### ⚡ Quick Actions")
        if st.button("🔄 New Conversation"):
            st.session_state.enhanced_conversation = []
            st.session_state.current_context = ConversationContext.GENERAL_HEALTH
            st.rerun()
        
        if st.button("📥 Export Chat"):
            # Export conversation as JSON
            export_data = {
                'timestamp': datetime.now().isoformat(),
                'conversation': st.session_state.enhanced_conversation
            }
            st.download_button(
                label="Download Conversation",
                data=json.dumps(export_data, indent=2),
                file_name=f"medical_chat_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )
    
    # Main chat interface
    chat_container = st.container()
    
    with chat_container:
        # Display conversation history
        for message in st.session_state.enhanced_conversation:
            if message['role'] == 'user':
                st.markdown(f"""
                <div class="enhanced-message" style="margin-left: 20%; background: #e3f2fd;">
                    <strong>You:</strong><br>
                    {message['content']}
                </div>
                """, unsafe_allow_html=True)
            else:
                # Show urgency badge if available
                urgency_html = ""
                if 'metadata' in message and 'urgency' in message['metadata']:
                    urgency = message['metadata']['urgency']
                    urgency_class = f"urgency-{urgency.lower()}"
                    urgency_html = f'<div class="context-badge {urgency_class}">{urgency}</div>'
                
                st.markdown(f"""
                <div class="enhanced-message" style="margin-right: 20%;">
                    {urgency_html}
                    <strong>MediBot AI:</strong><br>
                    {message['content']}
                </div>
                """, unsafe_allow_html=True)
                
                # Show follow-up questions if available
                if 'metadata' in message and message['metadata'].get('follow_up_questions'):
                    st.markdown('<div class="follow-up-questions">', unsafe_allow_html=True)
                    st.markdown("**💡 Suggested follow-up questions:**")
                    for question in message['metadata']['follow_up_questions']:
                        if st.button(f"❓ {question}", key=f"followup_{hash(question)}"):
                            st.session_state.pending_input = question
                            st.rerun()
                    st.markdown('</div>', unsafe_allow_html=True)
    
    # Smart suggestions based on context
    st.markdown("### 💭 Quick Responses")
    
    suggestions = {
        ConversationContext.SYMPTOMS: [
            "I've been feeling tired lately",
            "I have a persistent headache",
            "My symptoms started 3 days ago",
            "The pain is getting worse"
        ],
        ConversationContext.MEDICATION: [
            "What are the side effects?",
            "Can I take this with food?",
            "Are there any interactions?",
            "What's the proper dosage?"
        ],
        ConversationContext.GENERAL_HEALTH: [
            "I'd like a general health check",
            "What preventive measures should I take?",
            "How can I improve my health?",
            "I have a question about vaccines"
        ]
    }
    
    current_suggestions = suggestions.get(st.session_state.current_context, suggestions[ConversationContext.GENERAL_HEALTH])
    
    st.markdown('<div class="smart-suggestions">', unsafe_allow_html=True)
    cols = st.columns(len(current_suggestions))
    for idx, (col, suggestion) in enumerate(zip(cols, current_suggestions)):
        with col:
            if st.button(suggestion, key=f"suggestion_{idx}"):
                st.session_state.pending_input = suggestion
                st.rerun()
    st.markdown('</div>', unsafe_allow_html=True)
    
    # Enhanced input area
    col1, col2, col3 = st.columns([5, 1, 1])
    
    with col1:
        # Check for pending input
        default_value = ""
        if hasattr(st.session_state, 'pending_input'):
            default_value = st.session_state.pending_input
            del st.session_state.pending_input
        
        user_input = st.text_area(
            "How can I help you today?",
            height=100,
            placeholder="Describe your symptoms, ask questions, or share your health concerns...",
            key="enhanced_input",
            value=default_value
        )
    
    with col2:
        st.markdown("<br>", unsafe_allow_html=True)
        send_button = st.button("📤 Send", key="send_enhanced", type="primary")
    
    with col3:
        st.markdown("<br>", unsafe_allow_html=True)
        voice_button = st.button("🎤 Voice", key="voice_enhanced")
    
    # Process input
    if send_button and user_input:
        # Add user message
        st.session_state.enhanced_conversation.append({
            'role': 'user',
            'content': user_input,
            'timestamp': datetime.now().isoformat()
        })
        
        # Show typing indicator
        with st.spinner("MediBot AI is thinking..."):
            # Process message
            result = st.session_state.chat_system.process_message(
                user_input,
                st.session_state.get('current_patient')
            )
            
            # Update context
            st.session_state.current_context = ConversationContext(result['context'])
            
            # Add assistant response
            st.session_state.enhanced_conversation.append({
                'role': 'assistant',
                'content': result['response'],
                'timestamp': datetime.now().isoformat(),
                'metadata': {
                    'context': result['context'],
                    'urgency': result['urgency'],
                    'entities': result['entities'],
                    'follow_up_questions': result['follow_up_questions'],
                    'recommendations': result['recommendations'],
                    'topics': result['entities'].get('symptoms', []) + result['entities'].get('body_parts', [])
                }
            })
        
        st.rerun()
    
    # Show recommendations if available
    if st.session_state.enhanced_conversation:
        last_message = st.session_state.enhanced_conversation[-1]
        if last_message['role'] == 'assistant' and 'metadata' in last_message:
            recommendations = last_message['metadata'].get('recommendations', [])
            if recommendations:
                st.markdown("### 📋 Recommendations")
                for rec in recommendations:
                    st.markdown(f"""
                    <div class="recommendation-card">
                        ✅ {rec}
                    </div>
                    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()