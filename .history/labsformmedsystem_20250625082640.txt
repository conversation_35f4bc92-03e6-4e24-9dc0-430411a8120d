st.header('Labsform Preceptorship')
    
    st.markdown("-------")


    # Dicionário de abreviações para exames
    exames_dict = {
        "creatinina": "Cr",
    "sódio": "Na",
    "potássio": "K",
    "CO2 total": "CO2 total",
    "TRANS.GLUTÂMICO OXALACÉTICA": "TGO",
    "TRANS.GLUTÂMICO PIRÚVICA": "TGP",
    "FOSFATASE ALCALINA": "FA",
    "Gama-Glutamil Transferase": "GGT",
    "BILIRRUBINA TOTAL": "BT",
    "BILIRRUBINA DIRETA": "BD",
    "TEMPO DE PROTROMBINA": "TP",
    "TEMPO DE TROMBOPLASTINA PARCIAL": "TTPA",
    "Hemoglobina": "Hb",
    "Hematócrito": "HT",
    "VOLUME CORPUSCULAR MÉDIO (V.C.M.)": "VCM",
    "CONCENTRAÇÃO DE HEMOGLOBINA CORPUSCULAR MÉDIA (C.H.C.M.)": "CHCM",
    "AMPLITUDE DE DISTRIBUIÇÃO ERITROCITÁRIA (R.D.W.)": "RDW",
    "Leucócitos": "Leuco",
    "Neutrófilos": "N",
    "Linfócitos": "L",
    "Monócitos": "Mono",
    "Eosinófilos": "Eos",
    "Basófilos": "Bas",
    "Plaquetas": "plaq",
    "Glicose": "Glicose",
    "Ureia": "Ureia",
    "Ácido Úrico": "AU",
    "Colesterol Total": "CT",
    "HDL Colesterol": "HDL",
    "LDL Colesterol": "LDL",
    "Triglicerídeos": "Triglicerídeos",
    "Proteína C-reativa": "PCR",
    "Albumina": "Alb",
    "Ferro Sérico": "Fe",
    "Capacidade Total de Ligação do Ferro (CTLF)": "CTLF",
    "Ferritina": "Ferritina",
    "Vitamina B12": "Vit B12",
    "Ácido Fólico": "Ác Fólico",
    "Cálcio": "Ca",
    "Cálcio Ionizado": "Ca++",
    "Magnésio": "Mg",
    "Fósforo": "P",
    "Paratormônio (PTH)": "PTH",
    "Hormônio Tireoestimulante (TSH)": "TSH",
    "Tiroxina Livre (T4 Livre)": "T4L",
    "Triiodotironina Livre (T3 Livre)": "T3L",
    "Hormônio Luteinizante (LH)": "LH",
    "Hormônio Folículo-estimulante (FSH)": "FSH",
    "Testosterona Total": "Testo",
    "Progesterona": "Prog",
    "Estradiol": "E2",
    "Antígeno Prostático Específico (PSA)": "PSA",
    "D-dímero": "D-dímero",
    "Lactato Desidrogenase (LDH)": "LDH",
    "Troponina": "Tn",
    "Creatina Quinase (CK)": "CK",
    "CK-MB": "CK-MB",
    "NT-proBNP": "NT-proBNP",
    "Proteínas Totais": "PT",
    "IgM": "IgM",
    "IgG": "IgG",
    "IgA": "IgA",
    "Imunoglobulina E (IgE)": "IgE",
    "Amilase": "Amilase",
    "Lipase": "Lipase",
    "Ácido Lático": "Lac",
    "Hormônio de Crescimento (GH)": "GH",
    "Cortisol": "Cort",
    "Insulina": "Insulina",
    "Peptídeo C": "Pept C",
    "Prolactina": "Prol",
    "Vitamina D": "Vit D",
    "Homocisteína": "Hcys",
    "Anticorpos Antinucleares (ANA)": "ANA",
    "Proteína Sérica A Amiloide (SAA)": "SAA",
    "Fator Reumatoide (FR)": "FR",
    "Anticorpos Anti-CCP": "Anti-CCP"
    }

    # Carregar valores de referência
    @st.cache_data
    def load_reference_values():
        return {
            "cr": {"min": 0.6, "max": 1.2, "unit": "mg/dL"},
            "Na": {"min": 135, "max": 145, "unit": "mEq/L"},
            "K": {"min": 3.5, "max": 5.0, "unit": "mEq/L"},
            "TGO": {"min": 1, "max": 34, "unit": "U/L"},
            "TGP": {"min": 10, "max": 49, "unit": "U/L"},
            "FA": {"min": 44, "max": 147, "unit": "U/L"},
            "GGT": {"min": 9, "max": 48, "unit": "U/L"},
            "bilirrub. total": {"min": 0.3, "max": 1.2, "unit": "mg/dL"},
            "BD": {"min": 0.0, "max": 0.3, "unit": "mg/dL"},
            "TP": {"min": 11, "max": 13.5, "unit": "segundos"},
            "TTPA": {"min": 25, "max": 35, "unit": "segundos"},
            "Hb": {"min": 13.5, "max": 17.5, "unit": "g/dL"},
            "HT": {"min": 41, "max": 53, "unit": "%"},
            "VCM": {"min": 80, "max": 100, "unit": "fL"},
            "CHCM": {"min": 31, "max": 36, "unit": "%"},
            "RDW": {"min": 11.5, "max": 14.5, "unit": "%"},
            "Leuco": {"min": 4000, "max": 11000, "unit": "/mm³"},
            "N": {"min": 1800, "max": 7700, "unit": "/mm³"},
            "L": {"min": 1000, "max": 4800, "unit": "/mm³"},
            "plaq": {"min": 150000, "max": 450000, "unit": "/mm³"}
        }

    reference_values = load_reference_values()

    # Função para formatar exames
    def formatar_exames_para_prontuario(dados_exames):
        linhas = dados_exames.split('\n')
        exames_formatados = []
        
        data_atual = datetime.now().strftime("%d/%m/%y")
        exames_formatados.append(f"Labs {data_atual}:")
        
        for linha in linhas:
            for nome_completo, abreviacao in exames_dict.items():
                if nome_completo.lower() in linha.lower():
                    valor = re.search(r'[\d,.]+', linha)
                    if valor:
                        valor = valor.group().replace(',', '.')
                        if abreviacao in ["TP", "TTPA"]:
                            rni = re.search(r'RNI.*?(\d+\.\d+)', linha)
                            if rni:
                                valor += f" RNI {rni.group(1)}"
                        elif abreviacao in ["Leuco", "N", "L"]:
                            valor = re.sub(r'\D', '', valor)
                        elif abreviacao in ["Hb", "HT", "VCM", "CHCM", "RDW"]:
                            unidade = re.search(r'(g/dL|%|fL)', linha)
                            if unidade:
                                valor += f" {unidade.group(1)}"
                        exames_formatados.append(f"{abreviacao} {valor}")
                    break
        
        return ' | '.join(exames_formatados)

    # Função para extrair histórico
    def extrair_historico(dados_exames):
        historico = {}
        for nome_completo, abreviacao in exames_dict.items():
            padrao = rf"{nome_completo}.*?(\d{{2}}/\d{{2}}/\d{{4}})\s*:\s*([\d,.]+)"
            matches = re.findall(padrao, dados_exames, re.IGNORECASE | re.DOTALL)
            if matches:
                historico[abreviacao] = matches
        return historico

    # Função para formatar histórico
    def formatar_historico(historico):
        df = pd.DataFrame(columns=['Exame', 'Data', 'Valor'])
        for exame, valores in historico.items():
            for data, valor in valores:
                df = df.append({'Exame': exame, 'Data': datetime.strptime(data, '%d/%m/%Y'), 'Valor': float(valor.replace(',', '.'))}, ignore_index=True)
        return df.sort_values(['Exame', 'Data'], ascending=[True, False])

    # Função para analisar exames usando GPT-4
    def analisar_exames(dados_formatados, historico_df):
        prompt = f"""Analise os seguintes exames de laboratório e forneça um resumo detalhado:

        Exames atuais:
        {dados_formatados}

        Histórico de exames:
        {historico_df.to_string() if not historico_df.empty else "Sem histórico disponível"}

        Por favor, inclua:
        1. Uma visão geral do estado de saúde do paciente com base nos exames atuais e no histórico.
        2. Identificação de quaisquer valores anormais, sua significância clínica e evolução ao longo do tempo.
        3. Padrões ou tendências notáveis nos resultados ao longo do tempo.
        4. Recomendações para exames adicionais ou acompanhamento, se necessário.
        5. Possíveis diagnósticos diferenciais baseados nos resultados, se aplicável.
        6. Sugestões de intervenções ou mudanças no plano de tratamento, se apropriado.

        Formate sua resposta em tópicos para fácil leitura e inclua uma seção de "Pontos de Atenção" para destacar as informações mais críticas."""

        response = openai.ChatCompletion.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Você é um especialista médico em análise de exames laboratoriais com vasta experiência clínica."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=1000,
            temperature=0.1,
        )
        return response.choices[0].message['content'].strip()

    # Funço para gerar gráfico de tendências
    def gerar_grafico_tendencias(df, exames_selecionados):
        fig = make_subplots(rows=len(exames_selecionados), cols=1, subplot_titles=exames_selecionados)
        for i, exame in enumerate(exames_selecionados, start=1):
            df_exame = df[df['Exame'] == exame]
            fig.add_trace(go.Scatter(x=df_exame['Data'], y=df_exame['Valor'], mode='lines+markers', name=exame), row=i, col=1)
            
            # Adicionar linhas de referência
            if exame in reference_values:
                fig.add_hline(y=reference_values[exame]['min'], line_dash="dash", line_color="red", row=i, col=1)
                fig.add_hline(y=reference_values[exame]['max'], line_dash="dash", line_color="red", row=i, col=1)

        fig.update_layout(height=300*len(exames_selecionados), title_text="Tendências dos Exames", showlegend=False)
        return fig

    # Função para gerar heatmap de correlação
    def gerar_heatmap_correlacao(df):
        pivot_df = df.pivot(index='Data', columns='Exame', values='Valor')
        corr_matrix = pivot_df.corr()
        
        plt.figure(figsize=(12, 10))
        sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', vmin=-1, vmax=1, center=0)
        plt.title('Matriz de Correlação entre Exames')
        
        buf = BytesIO()
        plt.savefig(buf, format="png")
        buf.seek(0)
        return base64.b64encode(buf.getvalue()).decode()

    # Interface do usuário
    st.subheader("Formatador de Exames Laboratoriais com interpretação inteligente")
    dados_exames = st.text_area('Colete e copie a página com o resultado dos exames laboratoriais do seu paciente e cole aqui:', height=250)
    
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        mostrar_historico = st.checkbox('Mostrar histórico de exames', value=True)
    with col2:
        usar_ia = st.checkbox('Usar IA para análise', value=True)
    with col3:
        mostrar_grafico = st.checkbox('Mostrar gráfico de tendências', value=True)
    with col4:
        mostrar_correlacao = st.checkbox('Mostrar matriz de correlação', value=True)

    if st.button('Analisar Exames'):
        if dados_exames:
            with st.spinner('Processando exames...'):
                exames_formatados = formatar_exames_para_prontuario(dados_exames)
                historico = extrair_historico(dados_exames)
                df_historico = formatar_historico(historico) if historico else pd.DataFrame()

                st.subheader('Exames Formatados para Prontuário')
                st.code(exames_formatados, language='markdown')
                
                if not df_historico.empty:
                    if mostrar_historico:
                        st.subheader('Histórico de Exames')
                        st.dataframe(df_historico)
                    
                    if mostrar_grafico:
                        st.subheader('Gráfico de Tendências')
                        exames_disponiveis = df_historico['Exame'].unique()
                        exames_selecionados = st.multiselect('Selecione os exames para visualizar', exames_disponiveis, default=exames_disponiveis[:3])
                        if exames_selecionados:
                            fig = gerar_grafico_tendencias(df_historico, exames_selecionados)
                            st.plotly_chart(fig)
                    
                    if mostrar_correlacao:
                        st.subheader('Matriz de Correlação')
                        heatmap_img = gerar_heatmap_correlacao(df_historico)
                        st.image(heatmap_img, use_column_width=True)
                else:
                    st.info('Nenhum histrico de exames encontrado para análise gráfica.')
                
                if usar_ia:
                    st.subheader('Análise Inteligente:')
                    analise = analisar_exames(exames_formatados, df_historico)
                    st.markdown(analise)
                    st.warning('Esta análise é gerada por IA e não substitui a avaliação de um profissional de saúde.')
                
                # Opção para baixar resultados
                resultados_completos = {
                    "exames_formatados": exames_formatados,
                    "historico": df_historico.to_dict() if not df_historico.empty else None,
                    "analise_ia": analise if usar_ia else "Análise IA não solicitada"
                }
                
                st.download_button(
                    label="Baixar Resultados Completos (JSON)",
                    data=json.dumps(resultados_completos, indent=2, default=str),
                    file_name="resultados_exames.json",
                    mime="application/json"
                )
        else:
            st.error('Por favor, insira os dados dos exames.')

    # Adicionar uma seção de ajuda/FAQ
    with st.expander("Ajuda / FAQ"):
        st.markdown("""
        ### Como usar o LabsForm AI:
        1. Cole os dados dos exames no campo de texto.
        2. Selecione as opções desejadas (histórico, análise IA, gráfico, correlaão).
        3. Clique em 'Analisar Exames'.
        4. Revise os resultados e baixe-os se necessário.

        ### Perguntas Frequentes:
        - **P: Que tipos de exames são suportados?**
          R: O sistema suporta uma variedade de exames laboratoriais comuns, incluindo hemograma, função renal, função hepática e coagulação.

        - **P: Como interpretar o gráfico de tendências?**
          R: O gráfico mostra a evolução dos valores de exames específicos ao longo do tempo. As linhas tracejadas vermelhas indicam os limites de referência.

        - **P: O que significa a matriz de correlação?**
          R: A matriz de correlação mostra como os diferentes exames se relacionam entre si. Valores próximos a 1 ou -1 indicam forte correlação.

        - **P: A análise IA é confiável?**
          R: A análise IA é uma ferramenta de suporte e não deve substituir o julgamento clínico de um profissional de saúde.

        - **P: Como posso contribuir para melhorar o sistema?**
          R: Seu feedback é valioso! Use a seção de feedback abaixo para compartilhar suas sugestões ou relatar problemas.
        """)

    # Adicionar feedback do usuário
    with st.expander("Enviar Feedback"):
        feedback = st.text_area("Seu feedback é importante para melhorarmos o LabsForm AI. Por favor, compartilhe suas sugestões ou relate quaisquer problemas:")
        if st.button("Enviar Feedback"):
            if feedback:
                # Aqui você pode implementar a lógica para salvar o feedback (por exemplo, em um banco de dados ou enviando por email)
                st.success("Obrigado pelo seu feedback! Sua opinião é muito importante para nós.")
            else:
                st.warning("Por favor, escreva seu feedback antes de enviar.")